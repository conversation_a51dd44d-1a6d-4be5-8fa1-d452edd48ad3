
/* !!! This is code generated by Prisma. Do not edit directly. !!!
/* eslint-disable */

Object.defineProperty(exports, "__esModule", { value: true });

const {
  Decimal,
  objectEnumValues,
  makeStrictEnum,
  Public,
  getRuntime,
  skip
} = require('@prisma/client/runtime/index-browser.js')


const Prisma = {}

exports.Prisma = Prisma
exports.$Enums = {}

/**
 * Prisma Client JS version: 6.10.0
 * Query Engine version: aee10d5a411e4360c6d3445ce4810ca65adbf3e8
 */
Prisma.prismaVersion = {
  client: "6.10.0",
  engine: "aee10d5a411e4360c6d3445ce4810ca65adbf3e8"
}

Prisma.PrismaClientKnownRequestError = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`PrismaClientKnownRequestError is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)};
Prisma.PrismaClientUnknownRequestError = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`PrismaClientUnknownRequestError is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.PrismaClientRustPanicError = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`PrismaClientRustPanicError is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.PrismaClientInitializationError = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`PrismaClientInitializationError is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.PrismaClientValidationError = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`PrismaClientValidationError is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.Decimal = Decimal

/**
 * Re-export of sql-template-tag
 */
Prisma.sql = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`sqltag is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.empty = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`empty is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.join = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`join is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.raw = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`raw is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.validator = Public.validator

/**
* Extensions
*/
Prisma.getExtensionContext = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`Extensions.getExtensionContext is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.defineExtension = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`Extensions.defineExtension is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}

/**
 * Shorthand utilities for JSON filtering
 */
Prisma.DbNull = objectEnumValues.instances.DbNull
Prisma.JsonNull = objectEnumValues.instances.JsonNull
Prisma.AnyNull = objectEnumValues.instances.AnyNull

Prisma.NullTypes = {
  DbNull: objectEnumValues.classes.DbNull,
  JsonNull: objectEnumValues.classes.JsonNull,
  AnyNull: objectEnumValues.classes.AnyNull
}



/**
 * Enums
 */

exports.Prisma.TransactionIsolationLevel = makeStrictEnum({
  ReadUncommitted: 'ReadUncommitted',
  ReadCommitted: 'ReadCommitted',
  RepeatableRead: 'RepeatableRead',
  Serializable: 'Serializable'
});

exports.Prisma.UserScalarFieldEnum = {
  id: 'id',
  email: 'email',
  password: 'password',
  name: 'name',
  role: 'role',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.IptvEntryScalarFieldEnum = {
  id: 'id',
  name: 'name',
  type: 'type',
  host: 'host',
  port: 'port',
  username: 'username',
  password: 'password',
  macAddress: 'macAddress',
  portalUrl: 'portalUrl',
  m3uUrl: 'm3uUrl',
  fileName: 'fileName',
  filePath: 'filePath',
  expiryDate: 'expiryDate',
  notes: 'notes',
  tags: 'tags',
  category: 'category',
  isActive: 'isActive',
  viewCount: 'viewCount',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.AppScalarFieldEnum = {
  id: 'id',
  name: 'name',
  description: 'description',
  platform: 'platform',
  version: 'version',
  fileName: 'fileName',
  filePath: 'filePath',
  downloadUrl: 'downloadUrl',
  iconPath: 'iconPath',
  screenshots: 'screenshots',
  instructions: 'instructions',
  downloadCount: 'downloadCount',
  isActive: 'isActive',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.BlogPostScalarFieldEnum = {
  id: 'id',
  title: 'title',
  slug: 'slug',
  content: 'content',
  excerpt: 'excerpt',
  featuredImage: 'featuredImage',
  category: 'category',
  tags: 'tags',
  metaTitle: 'metaTitle',
  metaDescription: 'metaDescription',
  isPublished: 'isPublished',
  isFeatured: 'isFeatured',
  viewCount: 'viewCount',
  publishedAt: 'publishedAt',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.StaticPageScalarFieldEnum = {
  id: 'id',
  title: 'title',
  slug: 'slug',
  content: 'content',
  metaTitle: 'metaTitle',
  metaDescription: 'metaDescription',
  isPublished: 'isPublished',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.SiteSettingsScalarFieldEnum = {
  id: 'id',
  key: 'key',
  value: 'value',
  type: 'type',
  description: 'description',
  updatedAt: 'updatedAt'
};

exports.Prisma.DatabaseConfigScalarFieldEnum = {
  id: 'id',
  host: 'host',
  port: 'port',
  database: 'database',
  username: 'username',
  password: 'password',
  isActive: 'isActive',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.SortOrder = {
  asc: 'asc',
  desc: 'desc'
};

exports.Prisma.NullsOrder = {
  first: 'first',
  last: 'last'
};

exports.Prisma.UserOrderByRelevanceFieldEnum = {
  id: 'id',
  email: 'email',
  password: 'password',
  name: 'name'
};

exports.Prisma.IptvEntryOrderByRelevanceFieldEnum = {
  id: 'id',
  name: 'name',
  host: 'host',
  username: 'username',
  password: 'password',
  macAddress: 'macAddress',
  portalUrl: 'portalUrl',
  m3uUrl: 'm3uUrl',
  fileName: 'fileName',
  filePath: 'filePath',
  notes: 'notes',
  tags: 'tags',
  category: 'category'
};

exports.Prisma.AppOrderByRelevanceFieldEnum = {
  id: 'id',
  name: 'name',
  description: 'description',
  version: 'version',
  fileName: 'fileName',
  filePath: 'filePath',
  downloadUrl: 'downloadUrl',
  iconPath: 'iconPath',
  screenshots: 'screenshots',
  instructions: 'instructions'
};

exports.Prisma.BlogPostOrderByRelevanceFieldEnum = {
  id: 'id',
  title: 'title',
  slug: 'slug',
  content: 'content',
  excerpt: 'excerpt',
  featuredImage: 'featuredImage',
  category: 'category',
  tags: 'tags',
  metaTitle: 'metaTitle',
  metaDescription: 'metaDescription'
};

exports.Prisma.StaticPageOrderByRelevanceFieldEnum = {
  id: 'id',
  title: 'title',
  slug: 'slug',
  content: 'content',
  metaTitle: 'metaTitle',
  metaDescription: 'metaDescription'
};

exports.Prisma.SiteSettingsOrderByRelevanceFieldEnum = {
  id: 'id',
  key: 'key',
  value: 'value',
  description: 'description'
};

exports.Prisma.DatabaseConfigOrderByRelevanceFieldEnum = {
  id: 'id',
  host: 'host',
  database: 'database',
  username: 'username',
  password: 'password'
};
exports.Role = exports.$Enums.Role = {
  ADMIN: 'ADMIN',
  EDITOR: 'EDITOR'
};

exports.IptvType = exports.$Enums.IptvType = {
  XTREAM_CODES: 'XTREAM_CODES',
  STALKER_MAC: 'STALKER_MAC',
  M3U: 'M3U',
  FILE_UPLOAD: 'FILE_UPLOAD'
};

exports.Platform = exports.$Enums.Platform = {
  ANDROID: 'ANDROID',
  IOS: 'IOS',
  WINDOWS: 'WINDOWS',
  MAC: 'MAC',
  LINUX: 'LINUX',
  WEB: 'WEB'
};

exports.SettingType = exports.$Enums.SettingType = {
  TEXT: 'TEXT',
  NUMBER: 'NUMBER',
  BOOLEAN: 'BOOLEAN',
  JSON: 'JSON'
};

exports.Prisma.ModelName = {
  User: 'User',
  IptvEntry: 'IptvEntry',
  App: 'App',
  BlogPost: 'BlogPost',
  StaticPage: 'StaticPage',
  SiteSettings: 'SiteSettings',
  DatabaseConfig: 'DatabaseConfig'
};

/**
 * This is a stub Prisma Client that will error at runtime if called.
 */
class PrismaClient {
  constructor() {
    return new Proxy(this, {
      get(target, prop) {
        let message
        const runtime = getRuntime()
        if (runtime.isEdge) {
          message = `PrismaClient is not configured to run in ${runtime.prettyName}. In order to run Prisma Client on edge runtime, either:
- Use Prisma Accelerate: https://pris.ly/d/accelerate
- Use Driver Adapters: https://pris.ly/d/driver-adapters
`;
        } else {
          message = 'PrismaClient is unable to run in this browser environment, or has been bundled for the browser (running in `' + runtime.prettyName + '`).'
        }

        message += `
If this is unexpected, please open an issue: https://pris.ly/prisma-prisma-bug-report`

        throw new Error(message)
      }
    })
  }
}

exports.PrismaClient = PrismaClient

Object.assign(exports, Prisma)
