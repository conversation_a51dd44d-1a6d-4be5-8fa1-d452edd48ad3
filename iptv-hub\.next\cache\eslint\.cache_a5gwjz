[{"C:\\xampp\\htdocs\\IPTV\\iptv-hub\\src\\app\\api\\setup\\complete\\route.ts": "1", "C:\\xampp\\htdocs\\IPTV\\iptv-hub\\src\\app\\api\\setup\\test-database\\route.ts": "2", "C:\\xampp\\htdocs\\IPTV\\iptv-hub\\src\\app\\layout.tsx": "3", "C:\\xampp\\htdocs\\IPTV\\iptv-hub\\src\\app\\page.tsx": "4", "C:\\xampp\\htdocs\\IPTV\\iptv-hub\\src\\app\\setup\\page.tsx": "5", "C:\\xampp\\htdocs\\IPTV\\iptv-hub\\src\\components\\ui\\alert.tsx": "6", "C:\\xampp\\htdocs\\IPTV\\iptv-hub\\src\\components\\ui\\button.tsx": "7", "C:\\xampp\\htdocs\\IPTV\\iptv-hub\\src\\components\\ui\\card.tsx": "8", "C:\\xampp\\htdocs\\IPTV\\iptv-hub\\src\\components\\ui\\input.tsx": "9", "C:\\xampp\\htdocs\\IPTV\\iptv-hub\\src\\components\\ui\\label.tsx": "10", "C:\\xampp\\htdocs\\IPTV\\iptv-hub\\src\\lib\\auth.ts": "11", "C:\\xampp\\htdocs\\IPTV\\iptv-hub\\src\\lib\\db.ts": "12", "C:\\xampp\\htdocs\\IPTV\\iptv-hub\\src\\lib\\upload.ts": "13", "C:\\xampp\\htdocs\\IPTV\\iptv-hub\\src\\lib\\utils.ts": "14", "C:\\xampp\\htdocs\\IPTV\\iptv-hub\\src\\middleware.ts": "15", "C:\\xampp\\htdocs\\IPTV\\iptv-hub\\src\\types\\index.ts": "16"}, {"size": 3954, "mtime": 1750228259758, "results": "17", "hashOfConfig": "18"}, {"size": 1158, "mtime": 1750228240824, "results": "19", "hashOfConfig": "18"}, {"size": 689, "mtime": 1749441313472, "results": "20", "hashOfConfig": "18"}, {"size": 4086, "mtime": 1750227814336, "results": "21", "hashOfConfig": "18"}, {"size": 11427, "mtime": 1750228170564, "results": "22", "hashOfConfig": "18"}, {"size": 1584, "mtime": 1750228218767, "results": "23", "hashOfConfig": "18"}, {"size": 1835, "mtime": 1750228181709, "results": "24", "hashOfConfig": "18"}, {"size": 1849, "mtime": 1750228209680, "results": "25", "hashOfConfig": "18"}, {"size": 824, "mtime": 1750228188536, "results": "26", "hashOfConfig": "18"}, {"size": 710, "mtime": 1750228194884, "results": "27", "hashOfConfig": "18"}, {"size": 3447, "mtime": 1750228083101, "results": "28", "hashOfConfig": "18"}, {"size": 3734, "mtime": 1750228066539, "results": "29", "hashOfConfig": "18"}, {"size": 4698, "mtime": 1750228126095, "results": "30", "hashOfConfig": "18"}, {"size": 4244, "mtime": 1750228104159, "results": "31", "hashOfConfig": "18"}, {"size": 904, "mtime": 1750228570127, "results": "32", "hashOfConfig": "18"}, {"size": 2366, "mtime": 1750228048222, "results": "33", "hashOfConfig": "18"}, {"filePath": "34", "messages": "35", "suppressedMessages": "36", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1g0d6ew", {"filePath": "37", "messages": "38", "suppressedMessages": "39", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "40", "messages": "41", "suppressedMessages": "42", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "43", "messages": "44", "suppressedMessages": "45", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "46", "messages": "47", "suppressedMessages": "48", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "49", "messages": "50", "suppressedMessages": "51", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "52", "messages": "53", "suppressedMessages": "54", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "55", "messages": "56", "suppressedMessages": "57", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "58", "messages": "59", "suppressedMessages": "60", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "61", "messages": "62", "suppressedMessages": "63", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "64", "messages": "65", "suppressedMessages": "66", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "67", "messages": "68", "suppressedMessages": "69", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "70", "messages": "71", "suppressedMessages": "72", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "73", "messages": "74", "suppressedMessages": "75", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "76", "messages": "77", "suppressedMessages": "78", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "79", "messages": "80", "suppressedMessages": "81", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "C:\\xampp\\htdocs\\IPTV\\iptv-hub\\src\\app\\api\\setup\\complete\\route.ts", [], [], "C:\\xampp\\htdocs\\IPTV\\iptv-hub\\src\\app\\api\\setup\\test-database\\route.ts", [], [], "C:\\xampp\\htdocs\\IPTV\\iptv-hub\\src\\app\\layout.tsx", [], [], "C:\\xampp\\htdocs\\IPTV\\iptv-hub\\src\\app\\page.tsx", [], [], "C:\\xampp\\htdocs\\IPTV\\iptv-hub\\src\\app\\setup\\page.tsx", [], [], "C:\\xampp\\htdocs\\IPTV\\iptv-hub\\src\\components\\ui\\alert.tsx", [], [], "C:\\xampp\\htdocs\\IPTV\\iptv-hub\\src\\components\\ui\\button.tsx", [], [], "C:\\xampp\\htdocs\\IPTV\\iptv-hub\\src\\components\\ui\\card.tsx", [], [], "C:\\xampp\\htdocs\\IPTV\\iptv-hub\\src\\components\\ui\\input.tsx", [], [], "C:\\xampp\\htdocs\\IPTV\\iptv-hub\\src\\components\\ui\\label.tsx", [], [], "C:\\xampp\\htdocs\\IPTV\\iptv-hub\\src\\lib\\auth.ts", [], [], "C:\\xampp\\htdocs\\IPTV\\iptv-hub\\src\\lib\\db.ts", [], [], "C:\\xampp\\htdocs\\IPTV\\iptv-hub\\src\\lib\\upload.ts", [], [], "C:\\xampp\\htdocs\\IPTV\\iptv-hub\\src\\lib\\utils.ts", [], [], "C:\\xampp\\htdocs\\IPTV\\iptv-hub\\src\\middleware.ts", [], [], "C:\\xampp\\htdocs\\IPTV\\iptv-hub\\src\\types\\index.ts", [], []]