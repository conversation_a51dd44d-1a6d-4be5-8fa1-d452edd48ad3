// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "mysql"
  url      = env("DATABASE_URL")
}

model User {
  id        String   @id @default(cuid())
  email     String   @unique
  password  String
  name      String?
  role      Role     @default(ADMIN)
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@map("users")
}

model IptvEntry {
  id         String    @id @default(cuid())
  name       String
  type       IptvType
  host       String?
  port       Int?
  username   String?
  password   String?
  macAddress String?
  portalUrl  String?
  m3uUrl     String?
  fileName   String?
  filePath   String?
  expiryDate DateTime?
  notes      String?   @db.Text
  tags       String?
  category   String?
  isActive   Boolean   @default(true)
  viewCount  Int       @default(0)
  createdAt  DateTime  @default(now())
  updatedAt  DateTime  @updatedAt

  @@map("iptv_entries")
}

model App {
  id            String   @id @default(cuid())
  name          String
  description   String?  @db.Text
  platform      Platform
  version       String
  fileName      String?
  filePath      String?
  downloadUrl   String?
  iconPath      String?
  screenshots   String?  @db.Text // JSON array of screenshot paths
  instructions  String?  @db.Text
  downloadCount Int      @default(0)
  isActive      Boolean  @default(true)
  createdAt     DateTime @default(now())
  updatedAt     DateTime @updatedAt

  @@map("apps")
}

model BlogPost {
  id              String    @id @default(cuid())
  title           String
  slug            String    @unique
  content         String    @db.LongText
  excerpt         String?   @db.Text
  featuredImage   String?
  category        String?
  tags            String? // JSON array of tags
  metaTitle       String?
  metaDescription String?   @db.Text
  isPublished     Boolean   @default(false)
  isFeatured      Boolean   @default(false)
  viewCount       Int       @default(0)
  publishedAt     DateTime?
  createdAt       DateTime  @default(now())
  updatedAt       DateTime  @updatedAt

  @@map("blog_posts")
}

model StaticPage {
  id              String   @id @default(cuid())
  title           String
  slug            String   @unique
  content         String   @db.LongText
  metaTitle       String?
  metaDescription String?  @db.Text
  isPublished     Boolean  @default(true)
  createdAt       DateTime @default(now())
  updatedAt       DateTime @updatedAt

  @@map("static_pages")
}

model SiteSettings {
  id          String      @id @default(cuid())
  key         String      @unique
  value       String      @db.Text
  type        SettingType @default(TEXT)
  description String?
  updatedAt   DateTime    @updatedAt

  @@map("site_settings")
}

model DatabaseConfig {
  id        String   @id @default(cuid())
  host      String
  port      Int
  database  String
  username  String
  password  String // This will be encrypted
  isActive  Boolean  @default(false)
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@map("database_configs")
}

enum Role {
  ADMIN
  EDITOR
}

enum IptvType {
  XTREAM_CODES
  STALKER_MAC
  M3U
  FILE_UPLOAD
}

enum Platform {
  ANDROID
  IOS
  WINDOWS
  MAC
  LINUX
  WEB
}

enum SettingType {
  TEXT
  NUMBER
  BOOLEAN
  JSON
}
