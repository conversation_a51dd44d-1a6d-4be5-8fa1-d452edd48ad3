{"version": 3, "middleware": {"/": {"files": ["server/edge-runtime-webpack.js", "server/src/middleware.js"], "name": "src/middleware", "page": "/", "matchers": [{"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?(?:\\/((?!api|_next\\/static|_next\\/image|favicon.ico).*))(\\.json)?[\\/#\\?]?$", "originalSource": "/((?!api|_next/static|_next/image|favicon.ico).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "TQU1F4kvo1vkBoXL9gDCK", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "yVuMoZOQvS/FUz2nfDNIWQEhiIJypxf6mzzN4eJgTZ0=", "__NEXT_PREVIEW_MODE_ID": "ee66bc3e1411a5301045e532fc76ac09", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "fd802c49615991c2df79c50e498d79ba136250a9218277f22a069621c062a0df", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "8cef3f617c87aafb7aea2f480d921503212333cfb1bfdae1c581ecca64c6aa91"}}}, "functions": {}, "sortedMiddleware": ["/"]}